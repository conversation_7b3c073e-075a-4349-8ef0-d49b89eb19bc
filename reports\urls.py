from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    path('', views.ReportsHomeView.as_view(), name='home'),
    path('student-grades/', views.StudentGradesReportView.as_view(), name='student_grades'),
    path('attendance/', views.AttendanceReportView.as_view(), name='attendance'),
    path('class-performance/', views.ClassPerformanceReportView.as_view(), name='class_performance'),
    path('notifications/', views.NotificationListView.as_view(), name='notifications'),
    path('system-log/', views.SystemLogView.as_view(), name='system_log'),
    
    # API endpoints for charts
    path('api/attendance-chart/', views.attendance_chart_data, name='attendance_chart_data'),
    path('api/grades-chart/', views.grades_chart_data, name='grades_chart_data'),
]
