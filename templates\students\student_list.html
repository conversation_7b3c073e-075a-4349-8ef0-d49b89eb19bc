{% extends 'base.html' %}

{% block title %}قائمة الطلاب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-user-graduate me-2"></i>
        قائمة الطلاب
    </h1>
    <a href="{% url 'students:add' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة طالب جديد
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="اسم الطالب أو رقم الهوية...">
            </div>
            <div class="col-md-3">
                <label for="class" class="form-label">الصف</label>
                <select class="form-select" id="class" name="class">
                    <option value="">جميع الصفوف</option>
                    {% for class_obj in classes %}
                        <option value="{{ class_obj.id }}" 
                                {% if selected_class == class_obj.id|stringformat:"s" %}selected{% endif %}>
                            {{ class_obj }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
                <a href="{% url 'students:list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Students Table -->
<div class="card">
    <div class="card-body">
        {% if students %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الطالب</th>
                            <th>الاسم</th>
                            <th>الصف</th>
                            <th>ولي الأمر</th>
                            <th>رقم الهاتف</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                            <tr>
                                <td>
                                    <strong>{{ student.student_id }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if student.photo %}
                                            <img src="{{ student.photo.url }}" alt="{{ student.arabic_name }}" 
                                                 class="rounded-circle me-2" width="40" height="40">
                                        {% else %}
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        {% endif %}
                                        <div>
                                            <div class="fw-bold">{{ student.arabic_name }}</div>
                                            <small class="text-muted">{{ student.full_name }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if student.current_class %}
                                        <span class="badge bg-info">{{ student.current_class }}</span>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>{{ student.guardian_name }}</div>
                                    <small class="text-muted">{{ student.guardian_relation }}</small>
                                </td>
                                <td>{{ student.guardian_phone|default:"-" }}</td>
                                <td>{{ student.enrollment_date }}</td>
                                <td>
                                    {% if student.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'students:detail' student.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'students:edit' student.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'grades:student_grades' student.pk %}" 
                                           class="btn btn-sm btn-outline-info" title="الدرجات">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        <a href="{% url 'attendance:student_attendance' student.pk %}" 
                                           class="btn btn-sm btn-outline-success" title="الحضور">
                                            <i class="fas fa-calendar-check"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="صفحات النتائج">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد بيانات طلاب</h4>
                <p class="text-muted">لم يتم العثور على أي طلاب مطابقين لمعايير البحث</p>
                <a href="{% url 'students:add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول طالب
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
