from django.db import models
from django.contrib.auth.models import User


class Report(models.Model):
    """نموذج التقرير"""
    REPORT_TYPE_CHOICES = [
        ('student_grades', 'تقرير درجات الطلاب'),
        ('attendance', 'تقرير الحضور'),
        ('class_performance', 'تقرير أداء الصف'),
        ('teacher_performance', 'تقرير أداء المعلم'),
        ('monthly_summary', 'الملخص الشهري'),
        ('yearly_summary', 'الملخص السنوي'),
    ]
    
    name = models.CharField('اسم التقرير', max_length=200)
    report_type = models.CharField('نوع التقرير', max_length=50, choices=REPORT_TYPE_CHOICES)
    description = models.TextField('الوصف', blank=True)
    
    # فلاتر التقرير
    start_date = models.DateField('تاريخ البداية', blank=True, null=True)
    end_date = models.DateField('تاريخ النهاية', blank=True, null=True)
    class_filter = models.ForeignKey('classes.Class', on_delete=models.SET_NULL, 
                                   null=True, blank=True, verbose_name='الصف')
    subject_filter = models.ForeignKey('subjects.Subject', on_delete=models.SET_NULL, 
                                     null=True, blank=True, verbose_name='المادة')
    
    # معلومات الإنشاء
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='منشئ التقرير')
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    
    # ملف التقرير
    file_path = models.FileField('ملف التقرير', upload_to='reports/', blank=True)
    
    class Meta:
        verbose_name = 'تقرير'
        verbose_name_plural = 'التقارير'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_report_type_display()}"


class Notification(models.Model):
    """نموذج الإشعارات"""
    NOTIFICATION_TYPE_CHOICES = [
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('success', 'نجاح'),
        ('error', 'خطأ'),
    ]
    
    title = models.CharField('العنوان', max_length=200)
    message = models.TextField('الرسالة')
    notification_type = models.CharField('نوع الإشعار', max_length=20, 
                                       choices=NOTIFICATION_TYPE_CHOICES, default='info')
    
    # المستقبلون
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, 
                                verbose_name='المستقبل', blank=True, null=True)
    recipient_type = models.CharField('نوع المستقبل', max_length=20, 
                                    choices=[
                                        ('all', 'الجميع'),
                                        ('teachers', 'المعلمون'),
                                        ('students', 'الطلاب'),
                                        ('parents', 'أولياء الأمور'),
                                        ('staff', 'الموظفون'),
                                    ], default='all')
    
    # حالة الإشعار
    is_read = models.BooleanField('مقروء', default=False)
    is_sent = models.BooleanField('مرسل', default=False)
    
    # معلومات الإنشاء
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, 
                                 related_name='sent_notifications', verbose_name='المرسل')
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    read_at = models.DateTimeField('تاريخ القراءة', blank=True, null=True)
    
    class Meta:
        verbose_name = 'إشعار'
        verbose_name_plural = 'الإشعارات'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.recipient or self.recipient_type}"


class SystemLog(models.Model):
    """سجل النظام"""
    ACTION_CHOICES = [
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('view', 'عرض'),
        ('export', 'تصدير'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='المستخدم')
    action = models.CharField('الإجراء', max_length=20, choices=ACTION_CHOICES)
    model_name = models.CharField('اسم النموذج', max_length=100, blank=True)
    object_id = models.PositiveIntegerField('معرف الكائن', blank=True, null=True)
    description = models.TextField('الوصف', blank=True)
    ip_address = models.GenericIPAddressField('عنوان IP', blank=True, null=True)
    user_agent = models.TextField('معلومات المتصفح', blank=True)
    timestamp = models.DateTimeField('الوقت', auto_now_add=True)
    
    class Meta:
        verbose_name = 'سجل النظام'
        verbose_name_plural = 'سجلات النظام'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.timestamp}"
