from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Count
from .models import Class, ClassSubject
from students.models import Student


class ClassListView(LoginRequiredMixin, ListView):
    """قائمة الصفوف"""
    model = Class
    template_name = 'classes/class_list.html'
    context_object_name = 'classes'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Class.objects.select_related('grade', 'academic_year', 'class_teacher')
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(grade__name__icontains=search)
            )
        
        # فلترة حسب المرحلة
        grade = self.request.GET.get('grade')
        if grade:
            queryset = queryset.filter(grade_id=grade)
        
        return queryset.order_by('grade__level', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import Grade
        context['grades'] = Grade.objects.all()
        context['search'] = self.request.GET.get('search', '')
        context['selected_grade'] = self.request.GET.get('grade', '')
        return context


class ClassDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل الصف"""
    model = Class
    template_name = 'classes/class_detail.html'
    context_object_name = 'class_obj'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        class_obj = self.object
        
        # طلاب الصف
        context['students'] = Student.objects.filter(
            current_class=class_obj, is_active=True
        ).order_by('arabic_name')
        
        # مواد الصف
        context['subjects'] = ClassSubject.objects.filter(
            class_obj=class_obj
        ).select_related('subject', 'teacher')
        
        return context


class ClassCreateView(LoginRequiredMixin, CreateView):
    """إضافة صف جديد"""
    model = Class
    template_name = 'classes/class_form.html'
    fields = ['name', 'grade', 'academic_year', 'class_teacher', 'room_number', 'capacity']
    success_url = reverse_lazy('classes:list')


class ClassUpdateView(LoginRequiredMixin, UpdateView):
    """تعديل بيانات الصف"""
    model = Class
    template_name = 'classes/class_form.html'
    fields = ['name', 'grade', 'academic_year', 'class_teacher', 'room_number', 'capacity']
    success_url = reverse_lazy('classes:list')


class ClassDeleteView(LoginRequiredMixin, DeleteView):
    """حذف الصف"""
    model = Class
    template_name = 'classes/class_confirm_delete.html'
    success_url = reverse_lazy('classes:list')
