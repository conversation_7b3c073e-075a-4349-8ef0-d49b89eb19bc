from django.contrib import admin
from .models import Subject


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'subject_type', 'credit_hours', 'is_active']
    list_filter = ['subject_type', 'is_active']
    search_fields = ['name', 'code']
    list_editable = ['is_active']
    
    fieldsets = (
        ('معلومات المادة', {
            'fields': ('name', 'code', 'description')
        }),
        ('تفاصيل المادة', {
            'fields': ('subject_type', 'credit_hours', 'is_active')
        }),
    )
