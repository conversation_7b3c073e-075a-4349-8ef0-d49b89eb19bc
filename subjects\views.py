from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q
from .models import Subject


class SubjectListView(LoginRequiredMixin, ListView):
    """قائمة المواد"""
    model = Subject
    template_name = 'subjects/subject_list.html'
    context_object_name = 'subjects'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Subject.objects.filter(is_active=True)
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search)
            )
        
        # فلترة حسب نوع المادة
        subject_type = self.request.GET.get('type')
        if subject_type:
            queryset = queryset.filter(subject_type=subject_type)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['subject_types'] = Subject.SUBJECT_TYPE_CHOICES
        context['search'] = self.request.GET.get('search', '')
        context['selected_type'] = self.request.GET.get('type', '')
        return context


class SubjectDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل المادة"""
    model = Subject
    template_name = 'subjects/subject_detail.html'
    context_object_name = 'subject'


class SubjectCreateView(LoginRequiredMixin, CreateView):
    """إضافة مادة جديدة"""
    model = Subject
    template_name = 'subjects/subject_form.html'
    fields = ['name', 'code', 'description', 'subject_type', 'credit_hours']
    success_url = reverse_lazy('subjects:list')


class SubjectUpdateView(LoginRequiredMixin, UpdateView):
    """تعديل بيانات المادة"""
    model = Subject
    template_name = 'subjects/subject_form.html'
    fields = ['name', 'code', 'description', 'subject_type', 'credit_hours', 'is_active']
    success_url = reverse_lazy('subjects:list')


class SubjectDeleteView(LoginRequiredMixin, DeleteView):
    """حذف المادة"""
    model = Subject
    template_name = 'subjects/subject_confirm_delete.html'
    success_url = reverse_lazy('subjects:list')
