// JavaScript مخصص لنظام إدارة المدرسة

document.addEventListener('DOMContentLoaded', function() {
    
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء التنبيهات تلقائياً
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // تأكيد الحذف
    var deleteButtons = document.querySelectorAll('.btn-delete, .delete-confirm');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من الحذف؟ لا يمكن التراجع عن هذا الإجراء.')) {
                e.preventDefault();
            }
        });
    });
    
    // البحث المباشر
    var searchInputs = document.querySelectorAll('.live-search');
    searchInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            var searchTerm = this.value.toLowerCase();
            var targetTable = document.querySelector(this.dataset.target);
            if (targetTable) {
                var rows = targetTable.querySelectorAll('tbody tr');
                rows.forEach(function(row) {
                    var text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        });
    });
    
    // تحديد الكل
    var selectAllCheckboxes = document.querySelectorAll('.select-all');
    selectAllCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            var targetCheckboxes = document.querySelectorAll(this.dataset.target);
            targetCheckboxes.forEach(function(target) {
                target.checked = checkbox.checked;
            });
        });
    });
    
    // عداد الأحرف
    var textareas = document.querySelectorAll('textarea[data-max-length]');
    textareas.forEach(function(textarea) {
        var maxLength = parseInt(textarea.dataset.maxLength);
        var counter = document.createElement('small');
        counter.className = 'text-muted';
        textarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            var remaining = maxLength - textarea.value.length;
            counter.textContent = remaining + ' حرف متبقي';
            if (remaining < 10) {
                counter.className = 'text-danger';
            } else {
                counter.className = 'text-muted';
            }
        }
        
        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
    
    // تحميل الصور
    var imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            var file = this.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var preview = document.querySelector(input.dataset.preview);
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    });
    
    // تبديل الشريط الجانبي في الشاشات الصغيرة
    var sidebarToggle = document.querySelector('.sidebar-toggle');
    var sidebar = document.querySelector('.sidebar');
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
        
        // إغلاق الشريط الجانبي عند النقر خارجه
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
    }
    
    // تحديث الوقت
    var timeElements = document.querySelectorAll('.live-time');
    if (timeElements.length > 0) {
        function updateTime() {
            var now = new Date();
            var timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            timeElements.forEach(function(element) {
                element.textContent = timeString;
            });
        }
        updateTime();
        setInterval(updateTime, 60000); // تحديث كل دقيقة
    }
    
    // تحميل البيانات بـ AJAX
    var ajaxForms = document.querySelectorAll('.ajax-form');
    ajaxForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(form);
            var submitButton = form.querySelector('button[type="submit"]');
            var originalText = submitButton.textContent;
            
            // إظهار حالة التحميل
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading"></span> جاري الحفظ...';
            
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم الحفظ بنجاح', 'success');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    }
                } else {
                    showAlert(data.message || 'حدث خطأ', 'danger');
                }
            })
            .catch(error => {
                showAlert('حدث خطأ في الاتصال', 'danger');
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        });
    });
    
    // إظهار التنبيهات
    function showAlert(message, type = 'info') {
        var alertContainer = document.querySelector('.alert-container') || document.body;
        var alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.insertBefore(alert, alertContainer.firstChild);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    }
    
    // تحديث الإحصائيات المباشرة
    var statsElements = document.querySelectorAll('.live-stats');
    if (statsElements.length > 0) {
        function updateStats() {
            fetch('/api/stats/')
            .then(response => response.json())
            .then(data => {
                statsElements.forEach(function(element) {
                    var stat = element.dataset.stat;
                    if (data[stat] !== undefined) {
                        element.textContent = data[stat];
                    }
                });
            })
            .catch(error => {
                console.log('خطأ في تحديث الإحصائيات:', error);
            });
        }
        
        // تحديث كل 5 دقائق
        setInterval(updateStats, 300000);
    }
    
    // تحسين الأداء - lazy loading للصور
    var images = document.querySelectorAll('img[data-src]');
    if (images.length > 0 && 'IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    }
    
    // تحسين تجربة المستخدم - حفظ حالة النماذج
    var forms = document.querySelectorAll('form[data-save-state]');
    forms.forEach(function(form) {
        var formId = form.id || 'form_' + Math.random().toString(36).substr(2, 9);
        
        // استرجاع البيانات المحفوظة
        var savedData = localStorage.getItem('form_' + formId);
        if (savedData) {
            try {
                var data = JSON.parse(savedData);
                Object.keys(data).forEach(function(name) {
                    var field = form.querySelector('[name="' + name + '"]');
                    if (field && field.type !== 'password') {
                        field.value = data[name];
                    }
                });
            } catch (e) {
                console.log('خطأ في استرجاع بيانات النموذج');
            }
        }
        
        // حفظ البيانات عند التغيير
        form.addEventListener('input', function() {
            var formData = new FormData(form);
            var data = {};
            for (var pair of formData.entries()) {
                if (pair[0] !== 'csrfmiddlewaretoken') {
                    data[pair[0]] = pair[1];
                }
            }
            localStorage.setItem('form_' + formId, JSON.stringify(data));
        });
        
        // مسح البيانات المحفوظة عند الإرسال
        form.addEventListener('submit', function() {
            localStorage.removeItem('form_' + formId);
        });
    });
    
});

// دوال مساعدة
window.SchoolManagement = {
    
    // تأكيد الإجراء
    confirm: function(message, callback) {
        if (confirm(message || 'هل أنت متأكد؟')) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },
    
    // إظهار نافذة منبثقة
    showModal: function(title, content, size = 'md') {
        var modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-${size}">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        var bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
        
        return bsModal;
    },
    
    // تحميل محتوى بـ AJAX
    loadContent: function(url, container) {
        fetch(url)
        .then(response => response.text())
        .then(html => {
            document.querySelector(container).innerHTML = html;
        })
        .catch(error => {
            console.error('خطأ في تحميل المحتوى:', error);
        });
    }
    
};
