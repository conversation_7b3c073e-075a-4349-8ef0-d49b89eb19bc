from django.contrib import admin
from .models import Exam, StudentGrade, <PERSON><PERSON><PERSON>, StudentSemesterGrade


@admin.register(Exam)
class ExamAdmin(admin.ModelAdmin):
    list_display = ['name', 'exam_type', 'subject', 'class_obj', 'date', 'max_score']
    list_filter = ['exam_type', 'subject', 'class_obj', 'date']
    search_fields = ['name', 'subject__name', 'class_obj__name']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('معلومات الامتحان', {
            'fields': ('name', 'exam_type', 'subject', 'class_obj')
        }),
        ('تفاصيل الامتحان', {
            'fields': ('date', 'max_score', 'duration_minutes', 'description')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('subject', 'class_obj')


@admin.register(StudentGrade)
class StudentGradeAdmin(admin.ModelAdmin):
    list_display = ['student', 'exam', 'score', 'percentage', 'letter_grade', 'graded_by']
    list_filter = ['exam__exam_type', 'exam__subject', 'exam__class_obj', 'letter_grade']
    search_fields = ['student__arabic_name', 'exam__name']
    readonly_fields = ['percentage', 'letter_grade', 'graded_at']
    
    fieldsets = (
        ('معلومات الدرجة', {
            'fields': ('student', 'exam', 'score')
        }),
        ('تفاصيل إضافية', {
            'fields': ('notes', 'graded_by', 'graded_at', 'percentage', 'letter_grade')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'exam', 'graded_by')


@admin.register(Semester)
class SemesterAdmin(admin.ModelAdmin):
    list_display = ['name', 'academic_year', 'start_date', 'end_date', 'is_current']
    list_filter = ['academic_year', 'is_current']
    list_editable = ['is_current']
    ordering = ['-start_date']


@admin.register(StudentSemesterGrade)
class StudentSemesterGradeAdmin(admin.ModelAdmin):
    list_display = ['student', 'subject', 'semester', 'total_score', 'percentage', 'letter_grade']
    list_filter = ['semester', 'subject', 'letter_grade']
    search_fields = ['student__arabic_name', 'subject__name']
    readonly_fields = ['percentage', 'letter_grade']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'subject', 'semester')
