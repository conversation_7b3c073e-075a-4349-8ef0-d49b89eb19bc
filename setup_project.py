#!/usr/bin/env python
"""
<PERSON>ript to set up Django school management project
"""
import os
import sys
import subprocess

def run_command(command):
    """Run a command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Running: {command}")
        if result.stdout:
            print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command {command}: {e}")
        return False

def main():
    # Install Django
    print("Installing Django...")
    if not run_command("pip install django"):
        print("Failed to install Django")
        return False
    
    # Create Django project
    print("Creating Django project...")
    if not run_command("django-admin startproject school_management ."):
        print("Failed to create Django project")
        return False
    
    print("Django project setup completed successfully!")
    return True

if __name__ == "__main__":
    main()
