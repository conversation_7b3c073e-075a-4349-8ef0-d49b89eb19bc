from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Count
from .models import Teacher
from classes.models import ClassSubject


class TeacherListView(LoginRequiredMixin, ListView):
    """قائمة المعلمين"""
    model = Teacher
    template_name = 'teachers/teacher_list.html'
    context_object_name = 'teachers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Teacher.objects.filter(is_active=True)
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(arabic_name__icontains=search) |
                Q(teacher_id__icontains=search) |
                Q(specialization__icontains=search)
            )
        
        # فلترة حسب التخصص
        specialization = self.request.GET.get('specialization')
        if specialization:
            queryset = queryset.filter(specialization__icontains=specialization)
        
        return queryset.order_by('arabic_name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['specializations'] = Teacher.objects.values_list(
            'specialization', flat=True
        ).distinct()
        context['search'] = self.request.GET.get('search', '')
        context['selected_specialization'] = self.request.GET.get('specialization', '')
        return context


class TeacherDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل المعلم"""
    model = Teacher
    template_name = 'teachers/teacher_detail.html'
    context_object_name = 'teacher'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        teacher = self.object
        
        # الصفوف والمواد التي يدرسها
        context['teaching_assignments'] = ClassSubject.objects.filter(
            teacher=teacher
        ).select_related('class_obj', 'subject')
        
        return context


class TeacherCreateView(LoginRequiredMixin, CreateView):
    """إضافة معلم جديد"""
    model = Teacher
    template_name = 'teachers/teacher_form.html'
    fields = [
        'teacher_id', 'first_name', 'last_name', 'arabic_name',
        'date_of_birth', 'gender', 'national_id', 'phone', 'email',
        'address', 'hire_date', 'qualification', 'specialization',
        'experience_years', 'photo', 'salary'
    ]
    success_url = reverse_lazy('teachers:list')


class TeacherUpdateView(LoginRequiredMixin, UpdateView):
    """تعديل بيانات المعلم"""
    model = Teacher
    template_name = 'teachers/teacher_form.html'
    fields = [
        'first_name', 'last_name', 'arabic_name', 'date_of_birth',
        'gender', 'phone', 'email', 'address', 'qualification',
        'specialization', 'experience_years', 'photo', 'salary', 'is_active'
    ]
    success_url = reverse_lazy('teachers:list')


class TeacherDeleteView(LoginRequiredMixin, DeleteView):
    """حذف المعلم"""
    model = Teacher
    template_name = 'teachers/teacher_confirm_delete.html'
    success_url = reverse_lazy('teachers:list')
