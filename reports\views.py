from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Count, Avg, Q
from django.http import HttpResponse, JsonResponse
from datetime import date, timedelta, datetime
import json

from .models import Report, Notification, SystemLog
from students.models import Student
from teachers.models import Teacher
from classes.models import Class
from grades.models import StudentGrade, Exam
from attendance.models import AttendanceRecord


class ReportsHomeView(LoginRequiredMixin, TemplateView):
    """الصفحة الرئيسية للتقارير"""
    template_name = 'reports/reports_home.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # إحصائيات سريعة
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.count()
        context['total_reports'] = Report.objects.count()
        
        # التقارير الأخيرة
        context['recent_reports'] = Report.objects.select_related('created_by')[:5]
        
        return context


class StudentGradesReportView(LoginRequiredMixin, TemplateView):
    """تقرير درجات الطلاب"""
    template_name = 'reports/student_grades_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # فلاتر
        class_id = self.request.GET.get('class')
        subject_id = self.request.GET.get('subject')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        
        # بناء الاستعلام
        grades = StudentGrade.objects.select_related('student', 'exam', 'exam__subject')
        
        if class_id:
            grades = grades.filter(student__current_class_id=class_id)
        if subject_id:
            grades = grades.filter(exam__subject_id=subject_id)
        if start_date:
            grades = grades.filter(exam__date__gte=start_date)
        if end_date:
            grades = grades.filter(exam__date__lte=end_date)
        
        # إحصائيات
        if grades.exists():
            context['average_score'] = grades.aggregate(avg=Avg('score'))['avg']
            context['total_grades'] = grades.count()
            context['highest_score'] = grades.order_by('-score').first()
            context['lowest_score'] = grades.order_by('score').first()
        
        context['grades'] = grades.order_by('-exam__date')[:100]  # آخر 100 درجة
        
        # خيارات الفلترة
        context['classes'] = Class.objects.all()
        from subjects.models import Subject
        context['subjects'] = Subject.objects.filter(is_active=True)
        
        return context


class AttendanceReportView(LoginRequiredMixin, TemplateView):
    """تقرير الحضور"""
    template_name = 'reports/attendance_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # فلاتر
        class_id = self.request.GET.get('class')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        
        # تاريخ افتراضي (آخر 30 يوم)
        if not start_date:
            start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = date.today().strftime('%Y-%m-%d')
        
        # بناء الاستعلام
        attendance = AttendanceRecord.objects.select_related('student')
        attendance = attendance.filter(date__range=[start_date, end_date])
        
        if class_id:
            attendance = attendance.filter(student__current_class_id=class_id)
        
        # إحصائيات الحضور
        total_records = attendance.count()
        present_count = attendance.filter(status='present').count()
        absent_count = attendance.filter(status='absent').count()
        late_count = attendance.filter(status='late').count()
        
        context.update({
            'total_records': total_records,
            'present_count': present_count,
            'absent_count': absent_count,
            'late_count': late_count,
            'attendance_percentage': (present_count / total_records * 100) if total_records > 0 else 0,
            'start_date': start_date,
            'end_date': end_date,
            'classes': Class.objects.all(),
        })
        
        # إحصائيات يومية
        daily_stats = []
        current_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        while current_date <= end_date_obj:
            day_attendance = attendance.filter(date=current_date)
            daily_stats.append({
                'date': current_date,
                'present': day_attendance.filter(status='present').count(),
                'absent': day_attendance.filter(status='absent').count(),
                'late': day_attendance.filter(status='late').count(),
            })
            current_date += timedelta(days=1)
        
        context['daily_stats'] = daily_stats
        
        return context


class ClassPerformanceReportView(LoginRequiredMixin, TemplateView):
    """تقرير أداء الصف"""
    template_name = 'reports/class_performance_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        class_id = self.request.GET.get('class')
        if class_id:
            class_obj = get_object_or_404(Class, id=class_id)
            context['selected_class'] = class_obj
            
            # طلاب الصف
            students = Student.objects.filter(current_class=class_obj, is_active=True)
            context['students_count'] = students.count()
            
            # متوسط درجات الصف
            class_grades = StudentGrade.objects.filter(student__current_class=class_obj)
            if class_grades.exists():
                context['class_average'] = class_grades.aggregate(avg=Avg('score'))['avg']
            
            # نسبة الحضور
            class_attendance = AttendanceRecord.objects.filter(student__current_class=class_obj)
            if class_attendance.exists():
                present_count = class_attendance.filter(status='present').count()
                total_count = class_attendance.count()
                context['attendance_rate'] = (present_count / total_count * 100) if total_count > 0 else 0
            
            # أفضل الطلاب
            top_students = students.annotate(
                avg_grade=Avg('studentgrade__score')
            ).order_by('-avg_grade')[:5]
            context['top_students'] = top_students
        
        context['classes'] = Class.objects.all()
        return context


class NotificationListView(LoginRequiredMixin, ListView):
    """قائمة الإشعارات"""
    model = Notification
    template_name = 'reports/notification_list.html'
    context_object_name = 'notifications'
    paginate_by = 20
    
    def get_queryset(self):
        return Notification.objects.filter(
            Q(recipient=self.request.user) | Q(recipient_type='all')
        ).order_by('-created_at')


class SystemLogView(LoginRequiredMixin, ListView):
    """سجل النظام"""
    model = SystemLog
    template_name = 'reports/system_log.html'
    context_object_name = 'logs'
    paginate_by = 50
    
    def get_queryset(self):
        queryset = SystemLog.objects.select_related('user')
        
        # فلترة حسب المستخدم
        user_id = self.request.GET.get('user')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # فلترة حسب الإجراء
        action = self.request.GET.get('action')
        if action:
            queryset = queryset.filter(action=action)
        
        return queryset.order_by('-timestamp')


def attendance_chart_data(request):
    """بيانات مخطط الحضور"""
    # آخر 7 أيام
    end_date = date.today()
    start_date = end_date - timedelta(days=6)
    
    data = []
    current_date = start_date
    
    while current_date <= end_date:
        day_attendance = AttendanceRecord.objects.filter(date=current_date)
        data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'present': day_attendance.filter(status='present').count(),
            'absent': day_attendance.filter(status='absent').count(),
            'late': day_attendance.filter(status='late').count(),
        })
        current_date += timedelta(days=1)
    
    return JsonResponse({'data': data})


def grades_chart_data(request):
    """بيانات مخطط الدرجات"""
    # توزيع الدرجات
    grades_distribution = {
        'A': StudentGrade.objects.filter(score__gte=90).count(),
        'B': StudentGrade.objects.filter(score__gte=80, score__lt=90).count(),
        'C': StudentGrade.objects.filter(score__gte=70, score__lt=80).count(),
        'D': StudentGrade.objects.filter(score__gte=60, score__lt=70).count(),
        'F': StudentGrade.objects.filter(score__lt=60).count(),
    }
    
    return JsonResponse({'data': grades_distribution})
