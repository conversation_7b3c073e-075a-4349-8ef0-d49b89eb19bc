from django.contrib import admin
from .models import AcademicYear, Grade, Class, ClassSubject


@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_date', 'end_date', 'is_current']
    list_filter = ['is_current']
    list_editable = ['is_current']
    ordering = ['-start_date']


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    list_display = ['name', 'level']
    ordering = ['level']


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['name', 'grade', 'academic_year', 'class_teacher', 'student_count', 'capacity']
    list_filter = ['grade', 'academic_year', 'class_teacher']
    search_fields = ['name', 'grade__name', 'class_teacher__arabic_name']
    
    fieldsets = (
        ('معلومات الصف', {
            'fields': ('name', 'grade', 'academic_year', 'class_teacher')
        }),
        ('تفاصيل إضافية', {
            'fields': ('room_number', 'capacity')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('grade', 'academic_year', 'class_teacher')


@admin.register(ClassSubject)
class ClassSubjectAdmin(admin.ModelAdmin):
    list_display = ['class_obj', 'subject', 'teacher', 'weekly_hours']
    list_filter = ['class_obj__grade', 'subject', 'teacher']
    search_fields = ['class_obj__name', 'subject__name', 'teacher__arabic_name']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('class_obj', 'subject', 'teacher')
