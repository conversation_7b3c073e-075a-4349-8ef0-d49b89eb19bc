from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Count
from .models import Student
from classes.models import Class
from grades.models import StudentGrade
from attendance.models import AttendanceRecord


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'students/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # إحصائيات عامة
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.count()
        
        # الطلاب الجدد هذا الشهر
        from datetime import date, timedelta
        this_month = date.today().replace(day=1)
        context['new_students_this_month'] = Student.objects.filter(
            enrollment_date__gte=this_month
        ).count()
        
        # آخر الطلاب المسجلين
        context['recent_students'] = Student.objects.filter(
            is_active=True
        ).order_by('-enrollment_date')[:5]
        
        return context


class StudentListView(LoginRequiredMixin, ListView):
    """قائمة الطلاب"""
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Student.objects.filter(is_active=True).select_related('current_class')
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(arabic_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(national_id__icontains=search)
            )
        
        # فلترة حسب الصف
        class_filter = self.request.GET.get('class')
        if class_filter:
            queryset = queryset.filter(current_class_id=class_filter)
        
        return queryset.order_by('arabic_name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['classes'] = Class.objects.all()
        context['search'] = self.request.GET.get('search', '')
        context['selected_class'] = self.request.GET.get('class', '')
        return context


class StudentDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل الطالب"""
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.object
        
        # آخر الدرجات
        context['recent_grades'] = StudentGrade.objects.filter(
            student=student
        ).select_related('exam', 'exam__subject').order_by('-graded_at')[:5]
        
        # سجل الحضور الأخير
        context['recent_attendance'] = AttendanceRecord.objects.filter(
            student=student
        ).select_related('subject').order_by('-date')[:10]
        
        return context


class StudentCreateView(LoginRequiredMixin, CreateView):
    """إضافة طالب جديد"""
    model = Student
    template_name = 'students/student_form.html'
    fields = [
        'student_id', 'first_name', 'last_name', 'arabic_name',
        'date_of_birth', 'gender', 'national_id', 'phone', 'email',
        'address', 'enrollment_date', 'current_class', 'guardian_name',
        'guardian_phone', 'guardian_email', 'guardian_relation',
        'photo', 'medical_notes'
    ]
    success_url = reverse_lazy('students:list')


class StudentUpdateView(LoginRequiredMixin, UpdateView):
    """تعديل بيانات الطالب"""
    model = Student
    template_name = 'students/student_form.html'
    fields = [
        'first_name', 'last_name', 'arabic_name', 'date_of_birth',
        'gender', 'phone', 'email', 'address', 'current_class',
        'guardian_name', 'guardian_phone', 'guardian_email',
        'guardian_relation', 'photo', 'medical_notes', 'is_active'
    ]
    success_url = reverse_lazy('students:list')


class StudentDeleteView(LoginRequiredMixin, DeleteView):
    """حذف الطالب"""
    model = Student
    template_name = 'students/student_confirm_delete.html'
    success_url = reverse_lazy('students:list')
