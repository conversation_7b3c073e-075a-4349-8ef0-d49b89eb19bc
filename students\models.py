from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse


class Student(models.Model):
    """نموذج الطالب"""
    GENDER_CHOICES = [
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    ]
    
    # معلومات شخصية
    student_id = models.Char<PERSON>ield('رقم الطالب', max_length=20, unique=True)
    first_name = models.Cha<PERSON><PERSON><PERSON>('الاسم الأول', max_length=50)
    last_name = models.Cha<PERSON><PERSON><PERSON>('اسم العائلة', max_length=50)
    arabic_name = models.Char<PERSON><PERSON>('الاسم بالعربية', max_length=100)
    date_of_birth = models.DateField('تاريخ الميلاد')
    gender = models.Char<PERSON>ield('الجنس', max_length=1, choices=GENDER_CHOICES)
    national_id = models.Char<PERSON>ield('رقم الهوية', max_length=20, unique=True)
    
    # معلومات الاتصال
    phone = models.Char<PERSON><PERSON>('رقم الهاتف', max_length=15, blank=True)
    email = models.EmailField('البريد الإلكتروني', blank=True)
    address = models.TextField('العنوان', blank=True)
    
    # معلومات أكاديمية
    enrollment_date = models.DateField('تاريخ التسجيل')
    current_class = models.ForeignKey('classes.Class', on_delete=models.SET_NULL, 
                                    null=True, blank=True, verbose_name='الصف الحالي')
    
    # معلومات ولي الأمر
    guardian_name = models.CharField('اسم ولي الأمر', max_length=100)
    guardian_phone = models.CharField('هاتف ولي الأمر', max_length=15)
    guardian_email = models.EmailField('بريد ولي الأمر', blank=True)
    guardian_relation = models.CharField('صلة القرابة', max_length=50, default='والد')
    
    # معلومات إضافية
    photo = models.ImageField('صورة الطالب', upload_to='students/photos/', blank=True)
    medical_notes = models.TextField('ملاحظات طبية', blank=True)
    is_active = models.BooleanField('نشط', default=True)
    
    # تواريخ النظام
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    
    class Meta:
        verbose_name = 'طالب'
        verbose_name_plural = 'الطلاب'
        ordering = ['last_name', 'first_name']
    
    def __str__(self):
        return f"{self.arabic_name} ({self.student_id})"
    
    def get_absolute_url(self):
        return reverse('students:detail', kwargs={'pk': self.pk})
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))


class StudentUser(models.Model):
    """ربط الطالب بحساب المستخدم"""
    student = models.OneToOneField(Student, on_delete=models.CASCADE, verbose_name='الطالب')
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    
    class Meta:
        verbose_name = 'حساب طالب'
        verbose_name_plural = 'حسابات الطلاب'
    
    def __str__(self):
        return f"حساب {self.student.arabic_name}"
