from django.contrib import admin
from .models import Student, StudentUser


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['student_id', 'arabic_name', 'current_class', 'guardian_name', 'is_active']
    list_filter = ['current_class', 'gender', 'is_active', 'enrollment_date']
    search_fields = ['student_id', 'arabic_name', 'first_name', 'last_name', 'national_id']
    list_editable = ['is_active']
    readonly_fields = ['created_at', 'updated_at', 'age']
    
    fieldsets = (
        ('المعلومات الشخصية', {
            'fields': ('student_id', 'first_name', 'last_name', 'arabic_name', 
                      'date_of_birth', 'age', 'gender', 'national_id', 'photo')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email', 'address')
        }),
        ('المعلومات الأكاديمية', {
            'fields': ('enrollment_date', 'current_class', 'is_active')
        }),
        ('معلومات ولي الأمر', {
            'fields': ('guardian_name', 'guardian_phone', 'guardian_email', 'guardian_relation')
        }),
        ('معلومات إضافية', {
            'fields': ('medical_notes',),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('current_class')


@admin.register(StudentUser)
class StudentUserAdmin(admin.ModelAdmin):
    list_display = ['student', 'user']
    search_fields = ['student__arabic_name', 'user__username']
