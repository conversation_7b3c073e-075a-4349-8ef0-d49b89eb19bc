from django.contrib import admin
from .models import Report, Notification, SystemLog


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'report_type', 'created_by', 'created_at']
    list_filter = ['report_type', 'created_at', 'class_filter', 'subject_filter']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('معلومات التقرير', {
            'fields': ('name', 'report_type', 'description')
        }),
        ('فلاتر التقرير', {
            'fields': ('start_date', 'end_date', 'class_filter', 'subject_filter')
        }),
        ('معلومات الإنشاء', {
            'fields': ('created_by', 'created_at', 'file_path')
        }),
    )


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'notification_type', 'recipient', 'recipient_type', 'is_read', 'created_at']
    list_filter = ['notification_type', 'recipient_type', 'is_read', 'is_sent', 'created_at']
    search_fields = ['title', 'message']
    readonly_fields = ['created_at', 'read_at']
    
    fieldsets = (
        ('محتوى الإشعار', {
            'fields': ('title', 'message', 'notification_type')
        }),
        ('المستقبلون', {
            'fields': ('recipient', 'recipient_type')
        }),
        ('الحالة', {
            'fields': ('is_read', 'is_sent', 'read_at')
        }),
        ('معلومات الإنشاء', {
            'fields': ('created_by', 'created_at')
        }),
    )


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'action', 'model_name', 'timestamp', 'ip_address']
    list_filter = ['action', 'model_name', 'timestamp']
    search_fields = ['user__username', 'description', 'ip_address']
    readonly_fields = ['timestamp']
    
    fieldsets = (
        ('معلومات الإجراء', {
            'fields': ('user', 'action', 'model_name', 'object_id', 'description')
        }),
        ('معلومات تقنية', {
            'fields': ('ip_address', 'user_agent', 'timestamp')
        }),
    )
