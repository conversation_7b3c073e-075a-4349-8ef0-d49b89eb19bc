from django.db import models
from django.urls import reverse


class Subject(models.Model):
    """نموذج المادة الدراسية"""
    SUBJECT_TYPE_CHOICES = [
        ('core', 'مادة أساسية'),
        ('elective', 'مادة اختيارية'),
        ('activity', 'نشاط'),
    ]
    
    name = models.Char<PERSON>ield('اسم المادة', max_length=100)
    code = models.Char<PERSON><PERSON>('رمز المادة', max_length=20, unique=True)
    description = models.TextField('الوصف', blank=True)
    subject_type = models.Cha<PERSON><PERSON><PERSON>('نوع المادة', max_length=20, choices=SUBJECT_TYPE_CHOICES, default='core')
    credit_hours = models.PositiveIntegerField('عدد الساعات المعتمدة', default=1)
    is_active = models.BooleanField('نشط', default=True)
    
    class Meta:
        verbose_name = 'مادة'
        verbose_name_plural = 'المواد'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_absolute_url(self):
        return reverse('subjects:detail', kwargs={'pk': self.pk})
