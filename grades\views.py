from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Avg
from .models import Exam, StudentGrade, StudentSemesterGrade
from students.models import Student


class GradeListView(LoginRequiredMixin, ListView):
    """قائمة الدرجات"""
    model = StudentGrade
    template_name = 'grades/grade_list.html'
    context_object_name = 'grades'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = StudentGrade.objects.select_related('student', 'exam', 'exam__subject')
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__arabic_name__icontains=search) |
                Q(exam__name__icontains=search)
            )
        
        return queryset.order_by('-graded_at')


class ExamListView(<PERSON>ginRequiredMixin, <PERSON>View):
    """قائمة الامتحانات"""
    model = Exam
    template_name = 'grades/exam_list.html'
    context_object_name = 'exams'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Exam.objects.select_related('subject', 'class_obj')
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(subject__name__icontains=search)
            )
        
        return queryset.order_by('-date')


class ExamDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل الامتحان"""
    model = Exam
    template_name = 'grades/exam_detail.html'
    context_object_name = 'exam'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = self.object
        
        # درجات الطلاب في هذا الامتحان
        context['student_grades'] = StudentGrade.objects.filter(
            exam=exam
        ).select_related('student').order_by('-score')
        
        # إحصائيات
        grades = StudentGrade.objects.filter(exam=exam)
        if grades.exists():
            context['average_score'] = grades.aggregate(avg=Avg('score'))['avg']
            context['highest_score'] = grades.order_by('-score').first()
            context['lowest_score'] = grades.order_by('score').first()
        
        return context


class ExamCreateView(LoginRequiredMixin, CreateView):
    """إضافة امتحان جديد"""
    model = Exam
    template_name = 'grades/exam_form.html'
    fields = ['name', 'exam_type', 'subject', 'class_obj', 'date', 'max_score', 'duration_minutes', 'description']
    success_url = reverse_lazy('grades:exam_list')


class StudentGradeView(LoginRequiredMixin, DetailView):
    """درجات طالب معين"""
    model = Student
    template_name = 'grades/student_grades.html'
    context_object_name = 'student'
    pk_url_kwarg = 'student_id'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.object
        
        # درجات الطالب
        context['student_grades'] = StudentGrade.objects.filter(
            student=student
        ).select_related('exam', 'exam__subject').order_by('-graded_at')
        
        # الدرجات الفصلية
        context['semester_grades'] = StudentSemesterGrade.objects.filter(
            student=student
        ).select_related('subject', 'semester').order_by('-semester__start_date')
        
        return context
