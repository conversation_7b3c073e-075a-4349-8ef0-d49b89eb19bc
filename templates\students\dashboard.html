{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar me-1"></i>
        {{ "now"|date:"Y/m/d" }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-user-graduate fa-2x mb-3"></i>
                <div class="stats-number">{{ total_students }}</div>
                <div class="h6">إجمالي الطلاب</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-school fa-2x mb-3"></i>
                <div class="stats-number">{{ total_classes }}</div>
                <div class="h6">إجمالي الصفوف</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-user-plus fa-2x mb-3"></i>
                <div class="stats-number">{{ new_students_this_month }}</div>
                <div class="h6">طلاب جدد هذا الشهر</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-percentage fa-2x mb-3"></i>
                <div class="stats-number">95%</div>
                <div class="h6">نسبة الحضور</div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Students -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    آخر الطلاب المسجلين
                </h5>
            </div>
            <div class="card-body">
                {% if recent_students %}
                    <div class="list-group list-group-flush">
                        {% for student in recent_students %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ student.arabic_name }}</h6>
                                    <small class="text-muted">{{ student.student_id }}</small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ student.enrollment_date }}</small>
                                    {% if student.current_class %}
                                        <br><span class="badge bg-primary">{{ student.current_class }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'students:list' %}" class="btn btn-outline-primary">
                            عرض جميع الطلاب
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-graduate fa-3x mb-3"></i>
                        <p>لا توجد بيانات طلاب</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'students:add' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة طالب جديد
                    </a>
                    <a href="{% url 'teachers:add' %}" class="btn btn-success">
                        <i class="fas fa-chalkboard-teacher me-2"></i>
                        إضافة معلم جديد
                    </a>
                    <a href="{% url 'classes:add' %}" class="btn btn-info">
                        <i class="fas fa-school me-2"></i>
                        إضافة صف جديد
                    </a>
                    <a href="{% url 'attendance:daily_add' %}" class="btn btn-warning">
                        <i class="fas fa-calendar-check me-2"></i>
                        تسجيل الحضور
                    </a>
                    <a href="{% url 'grades:exam_add' %}" class="btn btn-secondary">
                        <i class="fas fa-clipboard-list me-2"></i>
                        إضافة امتحان
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_students }}</h4>
                            <small class="text-muted">الطلاب</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-success">{{ total_classes }}</h4>
                            <small class="text-muted">الصفوف</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-info">15</h4>
                            <small class="text-muted">المعلمون</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-warning">8</h4>
                            <small class="text-muted">المواد</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-danger">25</h4>
                            <small class="text-muted">الامتحانات</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-secondary">95%</h4>
                        <small class="text-muted">نسبة الحضور</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
