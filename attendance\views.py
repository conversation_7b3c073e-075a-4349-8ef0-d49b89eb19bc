from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Count
from .models import AttendanceRecord, DailyAttendance, AttendanceSummary
from students.models import Student


class AttendanceListView(LoginRequiredMixin, ListView):
    """قائمة سجلات الحضور"""
    model = AttendanceRecord
    template_name = 'attendance/attendance_list.html'
    context_object_name = 'attendance_records'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = AttendanceRecord.objects.select_related('student', 'subject', 'recorded_by')
        
        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__arabic_name__icontains=search) |
                Q(student__student_id__icontains=search)
            )
        
        # فلترة حسب التاريخ
        date_filter = self.request.GET.get('date')
        if date_filter:
            queryset = queryset.filter(date=date_filter)
        
        # فلترة حسب الحالة
        status_filter = self.request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-date', 'student__arabic_name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = AttendanceRecord.STATUS_CHOICES
        context['search'] = self.request.GET.get('search', '')
        context['selected_date'] = self.request.GET.get('date', '')
        context['selected_status'] = self.request.GET.get('status', '')
        return context


class DailyAttendanceListView(LoginRequiredMixin, ListView):
    """قائمة الحضور اليومي"""
    model = DailyAttendance
    template_name = 'attendance/daily_attendance_list.html'
    context_object_name = 'daily_attendance'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = DailyAttendance.objects.select_related('class_obj', 'subject', 'teacher')
        
        # فلترة حسب التاريخ
        date_filter = self.request.GET.get('date')
        if date_filter:
            queryset = queryset.filter(date=date_filter)
        
        # فلترة حسب الصف
        class_filter = self.request.GET.get('class')
        if class_filter:
            queryset = queryset.filter(class_obj_id=class_filter)
        
        return queryset.order_by('-date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from classes.models import Class
        context['classes'] = Class.objects.all()
        context['selected_date'] = self.request.GET.get('date', '')
        context['selected_class'] = self.request.GET.get('class', '')
        return context


class DailyAttendanceDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل الحضور اليومي"""
    model = DailyAttendance
    template_name = 'attendance/daily_attendance_detail.html'
    context_object_name = 'daily_attendance'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        daily_attendance = self.object
        
        # سجلات الحضور لهذا اليوم
        context['attendance_records'] = AttendanceRecord.objects.filter(
            date=daily_attendance.date,
            subject=daily_attendance.subject,
            period=daily_attendance.period,
            student__current_class=daily_attendance.class_obj
        ).select_related('student').order_by('student__arabic_name')
        
        return context


class DailyAttendanceCreateView(LoginRequiredMixin, CreateView):
    """إضافة حضور يومي جديد"""
    model = DailyAttendance
    template_name = 'attendance/daily_attendance_form.html'
    fields = ['class_obj', 'date', 'subject', 'period', 'teacher']
    success_url = reverse_lazy('attendance:daily_list')


class StudentAttendanceView(LoginRequiredMixin, DetailView):
    """حضور طالب معين"""
    model = Student
    template_name = 'attendance/student_attendance.html'
    context_object_name = 'student'
    pk_url_kwarg = 'student_id'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.object
        
        # سجلات حضور الطالب
        context['attendance_records'] = AttendanceRecord.objects.filter(
            student=student
        ).select_related('subject').order_by('-date')[:30]  # آخر 30 سجل
        
        # ملخص الحضور
        context['attendance_summary'] = AttendanceSummary.objects.filter(
            student=student
        ).order_by('-year', '-month')[:6]  # آخر 6 أشهر
        
        # إحصائيات سريعة
        from datetime import date, timedelta
        last_30_days = date.today() - timedelta(days=30)
        recent_records = AttendanceRecord.objects.filter(
            student=student,
            date__gte=last_30_days
        )
        
        context['stats'] = {
            'total_days': recent_records.count(),
            'present_days': recent_records.filter(status='present').count(),
            'absent_days': recent_records.filter(status='absent').count(),
            'late_days': recent_records.filter(status='late').count(),
        }
        
        if context['stats']['total_days'] > 0:
            context['stats']['attendance_percentage'] = (
                context['stats']['present_days'] / context['stats']['total_days']
            ) * 100
        else:
            context['stats']['attendance_percentage'] = 0
        
        return context
