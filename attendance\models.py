from django.db import models
from django.urls import reverse


class AttendanceRecord(models.Model):
    """نموذج سجل الحضور"""
    STATUS_CHOICES = [
        ('present', 'حاضر'),
        ('absent', 'غائب'),
        ('late', 'متأخر'),
        ('excused', 'غياب بعذر'),
    ]
    
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, verbose_name='الطالب')
    date = models.DateField('التاريخ')
    status = models.Char<PERSON>ield('الحالة', max_length=10, choices=STATUS_CHOICES)
    subject = models.ForeignKey('subjects.Subject', on_delete=models.CASCADE, 
                              verbose_name='المادة', blank=True, null=True)
    period = models.PositiveIntegerField('الحصة', blank=True, null=True)
    notes = models.TextField('ملاحظات', blank=True)
    recorded_by = models.Foreign<PERSON>ey('teachers.Teacher', on_delete=models.SET_NULL, 
                                  null=True, verbose_name='المسجل')
    recorded_at = models.DateTimeField('وقت التسجيل', auto_now_add=True)
    
    class Meta:
        verbose_name = 'سجل حضور'
        verbose_name_plural = 'سجلات الحضور'
        unique_together = ['student', 'date', 'subject', 'period']
        ordering = ['-date', 'student']
    
    def __str__(self):
        return f"{self.student} - {self.date} - {self.get_status_display()}"


class DailyAttendance(models.Model):
    """نموذج الحضور اليومي للصف"""
    class_obj = models.ForeignKey('classes.Class', on_delete=models.CASCADE, verbose_name='الصف')
    date = models.DateField('التاريخ')
    subject = models.ForeignKey('subjects.Subject', on_delete=models.CASCADE, 
                              verbose_name='المادة', blank=True, null=True)
    period = models.PositiveIntegerField('الحصة', blank=True, null=True)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, 
                              null=True, verbose_name='المعلم')
    is_completed = models.BooleanField('مكتمل', default=False)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    
    class Meta:
        verbose_name = 'حضور يومي'
        verbose_name_plural = 'الحضور اليومي'
        unique_together = ['class_obj', 'date', 'subject', 'period']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.class_obj} - {self.date}"
    
    def get_absolute_url(self):
        return reverse('attendance:daily_detail', kwargs={'pk': self.pk})
    
    @property
    def total_students(self):
        return self.class_obj.student_set.filter(is_active=True).count()
    
    @property
    def present_count(self):
        return AttendanceRecord.objects.filter(
            date=self.date,
            subject=self.subject,
            period=self.period,
            student__current_class=self.class_obj,
            status='present'
        ).count()
    
    @property
    def absent_count(self):
        return AttendanceRecord.objects.filter(
            date=self.date,
            subject=self.subject,
            period=self.period,
            student__current_class=self.class_obj,
            status='absent'
        ).count()
    
    @property
    def late_count(self):
        return AttendanceRecord.objects.filter(
            date=self.date,
            subject=self.subject,
            period=self.period,
            student__current_class=self.class_obj,
            status='late'
        ).count()


class AttendanceSummary(models.Model):
    """نموذج ملخص الحضور الشهري"""
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, verbose_name='الطالب')
    month = models.PositiveIntegerField('الشهر')
    year = models.PositiveIntegerField('السنة')
    total_days = models.PositiveIntegerField('إجمالي الأيام', default=0)
    present_days = models.PositiveIntegerField('أيام الحضور', default=0)
    absent_days = models.PositiveIntegerField('أيام الغياب', default=0)
    late_days = models.PositiveIntegerField('أيام التأخير', default=0)
    excused_days = models.PositiveIntegerField('أيام الغياب بعذر', default=0)
    
    class Meta:
        verbose_name = 'ملخص حضور'
        verbose_name_plural = 'ملخصات الحضور'
        unique_together = ['student', 'month', 'year']
        ordering = ['-year', '-month']
    
    def __str__(self):
        return f"{self.student} - {self.month}/{self.year}"
    
    @property
    def attendance_percentage(self):
        if self.total_days > 0:
            return (self.present_days / self.total_days) * 100
        return 0
