from django.db import models
from django.contrib.auth.models import User, Group
from django.db.models.signals import post_save
from django.dispatch import receiver


class UserProfile(models.Model):
    """ملف المستخدم الموسع"""
    USER_TYPE_CHOICES = [
        ('admin', 'مدير النظام'),
        ('teacher', 'معلم'),
        ('student', 'طالب'),
        ('parent', 'ولي أمر'),
        ('staff', 'موظف'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    user_type = models.CharField('نوع المستخدم', max_length=20, choices=USER_TYPE_CHOICES)
    phone = models.CharField('رقم الهاتف', max_length=15, blank=True)
    avatar = models.ImageField('الصورة الشخصية', upload_to='avatars/', blank=True)
    date_of_birth = models.DateField('تاريخ الميلاد', blank=True, null=True)
    address = models.TextField('العنوان', blank=True)
    
    # إعدادات الحساب
    is_first_login = models.BooleanField('أول تسجيل دخول', default=True)
    last_activity = models.DateTimeField('آخر نشاط', auto_now=True)
    
    class Meta:
        verbose_name = 'ملف المستخدم'
        verbose_name_plural = 'ملفات المستخدمين'
    
    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} ({self.get_user_type_display()})"


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """إنشاء ملف المستخدم تلقائياً عند إنشاء مستخدم جديد"""
    if created:
        UserProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """حفظ ملف المستخدم عند حفظ المستخدم"""
    if hasattr(instance, 'userprofile'):
        instance.userprofile.save()


class Permission(models.Model):
    """صلاحيات مخصصة للنظام"""
    name = models.CharField('اسم الصلاحية', max_length=100)
    codename = models.CharField('الرمز', max_length=100, unique=True)
    description = models.TextField('الوصف', blank=True)
    
    class Meta:
        verbose_name = 'صلاحية'
        verbose_name_plural = 'الصلاحيات'
    
    def __str__(self):
        return self.name


class Role(models.Model):
    """أدوار المستخدمين"""
    name = models.CharField('اسم الدور', max_length=100)
    description = models.TextField('الوصف', blank=True)
    permissions = models.ManyToManyField(Permission, verbose_name='الصلاحيات', blank=True)
    is_active = models.BooleanField('نشط', default=True)
    
    class Meta:
        verbose_name = 'دور'
        verbose_name_plural = 'الأدوار'
    
    def __str__(self):
        return self.name


class UserRole(models.Model):
    """ربط المستخدم بالأدوار"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name='الدور')
    assigned_at = models.DateTimeField('تاريخ التعيين', auto_now_add=True)
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, 
                                  related_name='assigned_roles', verbose_name='معين بواسطة')
    
    class Meta:
        verbose_name = 'دور المستخدم'
        verbose_name_plural = 'أدوار المستخدمين'
        unique_together = ['user', 'role']
    
    def __str__(self):
        return f"{self.user.username} - {self.role.name}"
