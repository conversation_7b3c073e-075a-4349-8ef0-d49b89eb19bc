from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse


class Teacher(models.Model):
    """نموذج المعلم"""
    GENDER_CHOICES = [
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    ]
    
    QUALIFICATION_CHOICES = [
        ('diploma', 'دبلوم'),
        ('bachelor', 'بكالوريوس'),
        ('master', 'ماجستير'),
        ('phd', 'دكتوراه'),
    ]
    
    # معلومات شخصية
    teacher_id = models.Char<PERSON>ield('رقم المعلم', max_length=20, unique=True)
    first_name = models.Char<PERSON><PERSON>('الاسم الأول', max_length=50)
    last_name = models.Char<PERSON>ield('اسم العائلة', max_length=50)
    arabic_name = models.Char<PERSON>ield('الاسم بالعربية', max_length=100)
    date_of_birth = models.DateField('تاريخ الميلاد')
    gender = models.Char<PERSON><PERSON>('الجنس', max_length=1, choices=GENDER_CHOICES)
    national_id = models.CharField('رقم الهوية', max_length=20, unique=True)
    
    # معلومات الاتصال
    phone = models.CharField('رقم الهاتف', max_length=15)
    email = models.EmailField('البريد الإلكتروني')
    address = models.TextField('العنوان')
    
    # معلومات مهنية
    hire_date = models.DateField('تاريخ التوظيف')
    qualification = models.CharField('المؤهل', max_length=20, choices=QUALIFICATION_CHOICES)
    specialization = models.CharField('التخصص', max_length=100)
    experience_years = models.PositiveIntegerField('سنوات الخبرة', default=0)
    
    # معلومات إضافية
    photo = models.ImageField('صورة المعلم', upload_to='teachers/photos/', blank=True)
    salary = models.DecimalField('الراتب', max_digits=10, decimal_places=2, blank=True, null=True)
    is_active = models.BooleanField('نشط', default=True)
    
    # تواريخ النظام
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    
    class Meta:
        verbose_name = 'معلم'
        verbose_name_plural = 'المعلمون'
        ordering = ['last_name', 'first_name']
    
    def __str__(self):
        return f"{self.arabic_name} ({self.teacher_id})"
    
    def get_absolute_url(self):
        return reverse('teachers:detail', kwargs={'pk': self.pk})
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"


class TeacherUser(models.Model):
    """ربط المعلم بحساب المستخدم"""
    teacher = models.OneToOneField(Teacher, on_delete=models.CASCADE, verbose_name='المعلم')
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    
    class Meta:
        verbose_name = 'حساب معلم'
        verbose_name_plural = 'حسابات المعلمين'
    
    def __str__(self):
        return f"حساب {self.teacher.arabic_name}"
