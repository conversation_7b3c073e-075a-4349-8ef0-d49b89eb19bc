from django.contrib import admin
from .models import Teacher, TeacherUser


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ['teacher_id', 'arabic_name', 'specialization', 'qualification', 'is_active']
    list_filter = ['qualification', 'specialization', 'gender', 'is_active', 'hire_date']
    search_fields = ['teacher_id', 'arabic_name', 'first_name', 'last_name', 'national_id']
    list_editable = ['is_active']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('المعلومات الشخصية', {
            'fields': ('teacher_id', 'first_name', 'last_name', 'arabic_name', 
                      'date_of_birth', 'gender', 'national_id', 'photo')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email', 'address')
        }),
        ('المعلومات المهنية', {
            'fields': ('hire_date', 'qualification', 'specialization', 
                      'experience_years', 'salary', 'is_active')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TeacherUser)
class TeacherUserAdmin(admin.ModelAdmin):
    list_display = ['teacher', 'user']
    search_fields = ['teacher__arabic_name', 'user__username']
