from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.models import User
from .models import UserProfile


def login_view(request):
    """صفحة تسجيل الدخول"""
    if request.user.is_authenticated:
        return redirect('students:dashboard')
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            
            # تحديث آخر نشاط
            if hasattr(user, 'userprofile'):
                user.userprofile.save()
            
            # إعادة توجيه حسب نوع المستخدم
            if hasattr(user, 'userprofile'):
                if user.userprofile.user_type == 'admin':
                    return redirect('admin:index')
                elif user.userprofile.user_type == 'teacher':
                    return redirect('teachers:list')
                elif user.userprofile.user_type == 'student':
                    return redirect('students:dashboard')
            
            return redirect('students:dashboard')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render(request, 'accounts/login.html')


def logout_view(request):
    """تسجيل الخروج"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('accounts:login')


@login_required
def profile_view(request):
    """صفحة الملف الشخصي"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    
    if request.method == 'POST':
        # تحديث البيانات الأساسية
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()
        
        # تحديث الملف الشخصي
        profile.phone = request.POST.get('phone', '')
        profile.address = request.POST.get('address', '')
        
        if 'avatar' in request.FILES:
            profile.avatar = request.FILES['avatar']
        
        profile.save()
        messages.success(request, 'تم تحديث الملف الشخصي بنجاح')
        return redirect('accounts:profile')
    
    return render(request, 'accounts/profile.html', {'profile': profile})


@login_required
def change_password_view(request):
    """تغيير كلمة المرور"""
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        if not request.user.check_password(current_password):
            messages.error(request, 'كلمة المرور الحالية غير صحيحة')
        elif new_password != confirm_password:
            messages.error(request, 'كلمة المرور الجديدة غير متطابقة')
        elif len(new_password) < 8:
            messages.error(request, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        else:
            request.user.set_password(new_password)
            request.user.save()
            messages.success(request, 'تم تغيير كلمة المرور بنجاح')
            return redirect('accounts:login')
    
    return render(request, 'accounts/change_password.html')


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم حسب نوع المستخدم"""
    template_name = 'accounts/dashboard.html'
    
    def get_template_names(self):
        if hasattr(self.request.user, 'userprofile'):
            user_type = self.request.user.userprofile.user_type
            if user_type == 'teacher':
                return ['accounts/teacher_dashboard.html']
            elif user_type == 'student':
                return ['accounts/student_dashboard.html']
            elif user_type == 'parent':
                return ['accounts/parent_dashboard.html']
        
        return ['students/dashboard.html']
