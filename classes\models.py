from django.db import models
from django.urls import reverse


class AcademicYear(models.Model):
    """نموذج السنة الدراسية"""
    name = models.Char<PERSON><PERSON>('اسم السنة الدراسية', max_length=50)
    start_date = models.DateField('تاريخ البداية')
    end_date = models.DateField('تاريخ النهاية')
    is_current = models.BooleanField('السنة الحالية', default=False)
    
    class Meta:
        verbose_name = 'سنة دراسية'
        verbose_name_plural = 'السنوات الدراسية'
        ordering = ['-start_date']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if self.is_current:
            # إزالة العلامة من السنوات الأخرى
            AcademicYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Grade(models.Model):
    """نموذج المرحلة الدراسية"""
    name = models.Char<PERSON>ield('اسم المرحلة', max_length=50)
    level = models.PositiveIntegerField('المستوى')
    description = models.TextField('الوصف', blank=True)
    
    class Meta:
        verbose_name = 'مرحلة دراسية'
        verbose_name_plural = 'المراحل الدراسية'
        ordering = ['level']
    
    def __str__(self):
        return self.name


class Class(models.Model):
    """نموذج الصف"""
    name = models.CharField('اسم الصف', max_length=50)
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE, verbose_name='المرحلة')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, verbose_name='السنة الدراسية')
    class_teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, 
                                    null=True, blank=True, verbose_name='معلم الصف')
    room_number = models.CharField('رقم الغرفة', max_length=20, blank=True)
    capacity = models.PositiveIntegerField('السعة القصوى', default=30)
    
    class Meta:
        verbose_name = 'صف'
        verbose_name_plural = 'الصفوف'
        ordering = ['grade__level', 'name']
        unique_together = ['name', 'grade', 'academic_year']
    
    def __str__(self):
        return f"{self.grade.name} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('classes:detail', kwargs={'pk': self.pk})
    
    @property
    def student_count(self):
        return self.student_set.filter(is_active=True).count()
    
    @property
    def is_full(self):
        return self.student_count >= self.capacity


class ClassSubject(models.Model):
    """نموذج ربط الصف بالمادة والمعلم"""
    class_obj = models.ForeignKey(Class, on_delete=models.CASCADE, verbose_name='الصف')
    subject = models.ForeignKey('subjects.Subject', on_delete=models.CASCADE, verbose_name='المادة')
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE, verbose_name='المعلم')
    weekly_hours = models.PositiveIntegerField('عدد الحصص الأسبوعية', default=1)
    
    class Meta:
        verbose_name = 'مادة الصف'
        verbose_name_plural = 'مواد الصفوف'
        unique_together = ['class_obj', 'subject']
    
    def __str__(self):
        return f"{self.class_obj} - {self.subject} - {self.teacher}"
