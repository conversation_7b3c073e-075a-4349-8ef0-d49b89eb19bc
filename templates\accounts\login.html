<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المدرسة</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .school-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .welcome-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form -->
                <div class="col-md-6">
                    <div class="login-form">
                        <div class="text-center mb-4">
                            <i class="fas fa-graduation-cap text-primary" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 mb-1">تسجيل الدخول</h2>
                            <p class="text-muted">نظام إدارة المدرسة</p>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>
                                    اسم المستخدم
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="أدخل اسم المستخدم" required>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="أدخل كلمة المرور" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                هل نسيت كلمة المرور؟ 
                                <a href="#" class="text-primary">اضغط هنا</a>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Welcome Section -->
                <div class="col-md-6">
                    <div class="login-image h-100">
                        <div>
                            <i class="fas fa-school school-icon"></i>
                            <div class="welcome-text">مرحباً بك</div>
                            <div class="subtitle">في نظام إدارة المدرسة</div>
                            <div class="mt-4">
                                <p class="mb-2">
                                    <i class="fas fa-check me-2"></i>
                                    إدارة شاملة للطلاب والمعلمين
                                </p>
                                <p class="mb-2">
                                    <i class="fas fa-check me-2"></i>
                                    متابعة الدرجات والحضور
                                </p>
                                <p class="mb-2">
                                    <i class="fas fa-check me-2"></i>
                                    تقارير مفصلة وإحصائيات
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-check me-2"></i>
                                    واجهة سهلة الاستخدام
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
