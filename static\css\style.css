/* نمط مخصص لنظام إدارة المدرسة */

/* الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: var(--light-color);
    font-size: 14px;
    line-height: 1.6;
}

/* الروابط */
a {
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    text-decoration: none;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 1.5rem;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* الشارات */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 20px;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card .card-body {
    padding: 2rem;
    text-align: center;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 1rem 0;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-dismissible .btn-close {
    padding: 1rem 1.5rem;
}

/* التصفح */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

/* الشريط العلوي */
.navbar {
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* الصور الشخصية */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-lg {
    width: 80px;
    height: 80px;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    margin: 2rem 0;
}

/* الأيقونات */
.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.icon-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.icon-success {
    background-color: var(--success-color);
    color: white;
}

.icon-info {
    background-color: var(--info-color);
    color: white;
}

.icon-warning {
    background-color: var(--warning-color);
    color: white;
}

.icon-danger {
    background-color: var(--danger-color);
    color: white;
}

/* الحالات */
.status-active {
    color: var(--success-color);
}

.status-inactive {
    color: var(--danger-color);
}

.status-pending {
    color: var(--warning-color);
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* طباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
