from django.contrib import admin
from .models import AttendanceRecord, DailyAttendance, AttendanceSummary


@admin.register(AttendanceRecord)
class AttendanceRecordAdmin(admin.ModelAdmin):
    list_display = ['student', 'date', 'status', 'subject', 'period', 'recorded_by']
    list_filter = ['status', 'date', 'subject', 'recorded_by']
    search_fields = ['student__arabic_name', 'student__student_id']
    date_hierarchy = 'date'
    readonly_fields = ['recorded_at']
    
    fieldsets = (
        ('معلومات الحضور', {
            'fields': ('student', 'date', 'status')
        }),
        ('تفاصيل الحصة', {
            'fields': ('subject', 'period')
        }),
        ('معلومات إضافية', {
            'fields': ('notes', 'recorded_by', 'recorded_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'subject', 'recorded_by')


@admin.register(DailyAttendance)
class DailyAttendanceAdmin(admin.ModelAdmin):
    list_display = ['class_obj', 'date', 'subject', 'period', 'teacher', 'is_completed', 'present_count', 'absent_count']
    list_filter = ['date', 'class_obj', 'subject', 'is_completed']
    search_fields = ['class_obj__name', 'teacher__arabic_name']
    date_hierarchy = 'date'
    readonly_fields = ['created_at', 'total_students', 'present_count', 'absent_count', 'late_count']
    
    fieldsets = (
        ('معلومات الحضور اليومي', {
            'fields': ('class_obj', 'date', 'subject', 'period', 'teacher')
        }),
        ('الحالة', {
            'fields': ('is_completed',)
        }),
        ('الإحصائيات', {
            'fields': ('total_students', 'present_count', 'absent_count', 'late_count'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('class_obj', 'subject', 'teacher')


@admin.register(AttendanceSummary)
class AttendanceSummaryAdmin(admin.ModelAdmin):
    list_display = ['student', 'month', 'year', 'attendance_percentage', 'present_days', 'absent_days']
    list_filter = ['year', 'month']
    search_fields = ['student__arabic_name', 'student__student_id']
    readonly_fields = ['attendance_percentage']
    
    fieldsets = (
        ('معلومات الملخص', {
            'fields': ('student', 'month', 'year')
        }),
        ('إحصائيات الحضور', {
            'fields': ('total_days', 'present_days', 'absent_days', 'late_days', 'excused_days', 'attendance_percentage')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student')
