from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.urls import reverse


class Exam(models.Model):
    """نموذج الامتحان"""
    EXAM_TYPE_CHOICES = [
        ('quiz', 'اختبار قصير'),
        ('midterm', 'امتحان نصف الفصل'),
        ('final', 'امتحان نهائي'),
        ('assignment', 'واجب'),
        ('project', 'مشروع'),
    ]
    
    name = models.CharField('اسم الامتحان', max_length=100)
    exam_type = models.CharField('نوع الامتحان', max_length=20, choices=EXAM_TYPE_CHOICES)
    subject = models.ForeignKey('subjects.Subject', on_delete=models.CASCADE, verbose_name='المادة')
    class_obj = models.ForeignKey('classes.Class', on_delete=models.CASCADE, verbose_name='الصف')
    date = models.DateField('تاريخ الامتحان')
    max_score = models.PositiveIntegerField('الدرجة العظمى', default=100)
    duration_minutes = models.PositiveIntegerField('مدة الامتحان بالدقائق', blank=True, null=True)
    description = models.TextField('الوصف', blank=True)
    
    class Meta:
        verbose_name = 'امتحان'
        verbose_name_plural = 'الامتحانات'
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.name} - {self.subject} - {self.class_obj}"
    
    def get_absolute_url(self):
        return reverse('grades:exam_detail', kwargs={'pk': self.pk})


class StudentGrade(models.Model):
    """نموذج درجة الطالب"""
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, verbose_name='الطالب')
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, verbose_name='الامتحان')
    score = models.DecimalField('الدرجة', max_digits=5, decimal_places=2, 
                               validators=[MinValueValidator(0)])
    notes = models.TextField('ملاحظات', blank=True)
    graded_by = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, 
                                null=True, verbose_name='المصحح')
    graded_at = models.DateTimeField('تاريخ التصحيح', auto_now_add=True)
    
    class Meta:
        verbose_name = 'درجة طالب'
        verbose_name_plural = 'درجات الطلاب'
        unique_together = ['student', 'exam']
        ordering = ['-graded_at']
    
    def __str__(self):
        return f"{self.student} - {self.exam} - {self.score}"
    
    @property
    def percentage(self):
        return (self.score / self.exam.max_score) * 100 if self.exam.max_score > 0 else 0
    
    @property
    def letter_grade(self):
        percentage = self.percentage
        if percentage >= 90:
            return 'A'
        elif percentage >= 80:
            return 'B'
        elif percentage >= 70:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'


class Semester(models.Model):
    """نموذج الفصل الدراسي"""
    name = models.CharField('اسم الفصل', max_length=50)
    academic_year = models.ForeignKey('classes.AcademicYear', on_delete=models.CASCADE, verbose_name='السنة الدراسية')
    start_date = models.DateField('تاريخ البداية')
    end_date = models.DateField('تاريخ النهاية')
    is_current = models.BooleanField('الفصل الحالي', default=False)
    
    class Meta:
        verbose_name = 'فصل دراسي'
        verbose_name_plural = 'الفصول الدراسية'
        ordering = ['-start_date']
    
    def __str__(self):
        return f"{self.name} - {self.academic_year}"
    
    def save(self, *args, **kwargs):
        if self.is_current:
            # إزالة العلامة من الفصول الأخرى
            Semester.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class StudentSemesterGrade(models.Model):
    """نموذج درجة الطالب في الفصل"""
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, verbose_name='الطالب')
    subject = models.ForeignKey('subjects.Subject', on_delete=models.CASCADE, verbose_name='المادة')
    semester = models.ForeignKey(Semester, on_delete=models.CASCADE, verbose_name='الفصل')
    total_score = models.DecimalField('المجموع الكلي', max_digits=6, decimal_places=2, default=0)
    max_total_score = models.DecimalField('المجموع الأعظم', max_digits=6, decimal_places=2, default=100)
    
    class Meta:
        verbose_name = 'درجة فصلية'
        verbose_name_plural = 'الدرجات الفصلية'
        unique_together = ['student', 'subject', 'semester']
    
    def __str__(self):
        return f"{self.student} - {self.subject} - {self.semester}"
    
    @property
    def percentage(self):
        return (self.total_score / self.max_total_score) * 100 if self.max_total_score > 0 else 0
    
    @property
    def letter_grade(self):
        percentage = self.percentage
        if percentage >= 90:
            return 'A'
        elif percentage >= 80:
            return 'B'
        elif percentage >= 70:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'
